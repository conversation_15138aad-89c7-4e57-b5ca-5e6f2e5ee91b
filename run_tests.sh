#!/bin/bash

# Array Adder Test Runner Script
# This script builds and runs both CPU and OpenCL versions of the array adder

echo "=== Array Adder Performance Test Suite ==="
echo

# Check if we're in the right directory
if [ ! -f "arrayAdder.cpp" ] || [ ! -f "arrayAdderOpenCL.cpp" ]; then
    echo "Error: Please run this script from the GPUPractice directory"
    echo "Expected files: arrayAdder.cpp, arrayAdderOpenCL.cpp"
    exit 1
fi

# Create build directory
echo "Creating build directory..."
mkdir -p build
cd build

# Build with CMake
echo "Building with CMake..."
cmake .. || {
    echo "Error: CMake configuration failed"
    exit 1
}

make || {
    echo "Error: Build failed"
    exit 1
}

echo "Build successful!"
echo

# Copy kernel file for OpenCL version
cp ../addarr_kernel.cl .

# Check OpenCL availability
echo "=== OpenCL System Information ==="
if command -v clinfo &> /dev/null; then
    clinfo | head -20
else
    echo "clinfo not available - OpenCL may not be properly installed"
fi
echo

# Run original CPU version
echo "=== Running Original CPU Version ==="
echo "Testing single-threaded and multi-threaded CPU implementations..."
./arrayAdder | head -30
echo "... (output truncated)"
echo

# Run OpenCL version
echo "=== Running OpenCL GPU Version ==="
echo "Testing CPU + OpenCL GPU implementations..."
./arrayAdderOpenCL | head -30
echo "... (output truncated)"
echo

echo "=== Test Complete ==="
echo "Both versions have been tested successfully!"
echo
echo "Performance Notes:"
echo "- For small arrays, CPU is often faster due to GPU setup overhead"
echo "- For large arrays (>10000 elements), GPU should show significant speedup"
echo "- If no GPU is detected, OpenCL version falls back to CPU-only mode"
echo
echo "To run individual tests:"
echo "  ./arrayAdder          # CPU-only version"
echo "  ./arrayAdderOpenCL    # OpenCL version with GPU acceleration"
