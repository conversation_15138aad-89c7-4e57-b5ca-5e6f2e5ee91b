#include <iostream>
#include <cstdlib>
#include <array>
#include <random>
#include <ctime>
#include <chrono>
#include <thread>
#include <vector>

// Function declarations
float *addarr(float A[], float B[], int N);
float *addarr_multithreaded(float A[], float B[], int N);
void addarr_worker(float A[], float B[], float C[], int start, int end);
float *generateRandomArray(int N, float min = 0.0f, float max = 10.0f);
void printArray(const char* label, float* arr, int N);
void testRandomArrays(int N);
float * timed(float* arr(float[], float[], int), float A[], float B[], int N);
int main()
{
    std::cout << "\n--- Testing Random Arrays ---\n";
    for(int i = 2; i < INT32_MAX; i*=10) {
        std::cout << "Iteration: " << i << " - ";
        testRandomArrays(i);
    }
    return 0;
}

float *addarr(float A[], float B[], int N)
{
    float *C = (float *)malloc(N * sizeof(float));
    for (int i = 0; i < N; i++)
        C[i] = A[i] + B[i];
    return C;
}

// Worker function for multi-threaded array addition
void addarr_worker(float A[], float B[], float C[], int start, int end)
{
    for (int i = start; i < end; i++) {
        C[i] = A[i] + B[i];
    }
}

// Multi-threaded version of addarr
float *addarr_multithreaded(float A[], float B[], int N)
{
    // Allocate memory for result array
    float *C = (float *)malloc(N * sizeof(float));
    if (C == nullptr) {
        std::cerr << "Memory allocation failed in addarr_multithreaded!" << std::endl;
        return nullptr;
    }

    // Get number of hardware threads (cores)
    unsigned int num_threads = std::thread::hardware_concurrency();
    if (num_threads == 0) {
        num_threads = 4; // Default fallback if detection fails
    }

    // For small arrays, use single thread to avoid overhead
    if (static_cast<unsigned int>(N) < num_threads * 100) {
        for (int i = 0; i < N; i++) {
            C[i] = A[i] + B[i];
        }
        return C;
    }

    // Calculate work distribution
    int elements_per_thread = N / num_threads;
    int remaining_elements = N % num_threads;

    // Create thread vector
    std::vector<std::thread> threads;
    threads.reserve(num_threads);

    // Launch threads
    int current_start = 0;
    for (unsigned int t = 0; t < num_threads; t++) {
        int current_end = current_start + elements_per_thread;

        // Give remaining elements to the last thread
        if (t == num_threads - 1) {
            current_end += remaining_elements;
        }

        threads.emplace_back(addarr_worker, A, B, C, current_start, current_end);
        current_start = current_end;
    }

    // Wait for all threads to complete
    for (auto& thread : threads) {
        thread.join();
    }

    return C;
}

float *generateRandomArray(int N, float min, float max)
{
    // Seed the random number generator
    static bool seeded = false;
    if (!seeded) {
        std::srand(std::time(nullptr));
        seeded = true;
    }

    float *arr = (float *)malloc(N * sizeof(float));
    if (arr == nullptr) {
        std::cerr << "Memory allocation failed!" << std::endl;
        return nullptr;
    }

    for (int i = 0; i < N; i++) {
        // Generate random float between min and max
        float random = (float)std::rand() / RAND_MAX; // 0.0 to 1.0
        arr[i] = min + random * (max - min);
    }

    return arr;
}

void printArray(const char* label, float* arr, int N)
{
    // Only print for powers of 2 (and skip N=0 to avoid issues)
    if (N == 0 || (N & (N - 1)) != 0)
        return;
    std::cout << label << ": ";
    for (int i = 0; i < N; i++) {
        std::cout << arr[i] << " ";
    }
    std::cout << std::endl;
}

void testRandomArrays(int N=50)
{
    std::cout << "Testing random arrays of size " << N << std::endl;
    std::cout << "Generating two random arrays of size " << N << std::endl;

    float *A = generateRandomArray(N, 1.0f, 5.0f);
    float *B = generateRandomArray(N, 2.0f, 8.0f);

    if (A == nullptr || B == nullptr) {
        std::cerr << "Failed to generate random arrays!" << std::endl;
        return;
    }

    printArray("Array A", A, N);
    printArray("Array B", B, N);

    // Test single-threaded version
    std::cout << "Single-threaded: ";
    float *C_single = timed(addarr, A, B, N);

    // Test multi-threaded version
    std::cout << "Multi-threaded:  ";
    float *C_multi = timed(addarr_multithreaded, A, B, N);

    printArray("Sum A+B (single)", C_single, N);
    printArray("Sum A+B (multi)", C_multi, N);

    // Verify results are identical (for small arrays)
    if (N <= 16) {
        bool results_match = true;
        for (int i = 0; i < N; i++) {
            if (std::abs(C_single[i] - C_multi[i]) > 1e-6) {
                results_match = false;
                break;
            }
        }
        std::cout << "Results match: " << (results_match ? "YES" : "NO") << std::endl;
    }

    // Clean up memory
    free(A);
    free(B);
    free(C_single);
    free(C_multi);
}

float* timed(float* arr(float[], float[], int), float A[], float B[], int N) {

    auto start = std::chrono::high_resolution_clock::now();
    float *C = arr(A, B, N);
    auto end = std::chrono::high_resolution_clock::now();

    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    std::cout << "Time: " << duration.count() << " microseconds" << std::endl;

    return C;
}