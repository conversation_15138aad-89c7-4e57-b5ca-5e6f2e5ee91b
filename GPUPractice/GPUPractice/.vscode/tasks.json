{"tasks": [{"type": "cppbuild", "label": "C/C++: g++ build active file", "command": "/usr/bin/g++", "args": ["-fdiagnostics-color=always", "-g", "-pthread", "-O2", "${file}", "-o", "${fileDirname}/${fileBasenameNoExtension}"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "Task generated by <PERSON>bugger."}], "version": "2.0.0"}