# Array Adder - Performance Testing Tool

A C++ program that performs element-wise addition of floating-point arrays with performance benchmarking capabilities, now featuring **OpenCL GPU acceleration** for AMD GPUs.

## Overview

This program demonstrates array operations in C++ with multiple implementation approaches:
- **Single-threaded CPU** implementation
- **Multi-threaded CPU** implementation using std::thread
- **OpenCL GPU** implementation for AMD GPUs with automatic fallback to CPU

The program generates random floating-point arrays of increasing sizes and measures the time taken to perform element-wise addition operations across different computing approaches.

## Features

- **Array Addition**: Element-wise addition of two floating-point arrays
- **Random Array Generation**: Creates arrays filled with random floats within specified ranges
- **Performance Timing**: High-resolution timing of array operations using `std::chrono`
- **Memory Management**: Proper dynamic memory allocation and deallocation
- **Scalable Testing**: Tests arrays of exponentially increasing sizes (powers of 10)
- **Selective Output**: Only displays array contents for power-of-2 sizes to reduce output clutter
- **OpenCL GPU Acceleration**: Utilizes AMD GPU for parallel computation with automatic fallback
- **Multi-Platform Support**: Works with AMD GPUs, Intel GPUs, and CPU-only systems
- **Performance Comparison**: Side-by-side timing comparison of CPU vs GPU implementations

## Available Implementations

### 1. Original CPU Version (`arrayAdder.cpp`)
- Single-threaded and multi-threaded CPU implementations
- Basic performance testing

### 2. OpenCL GPU Version (`arrayAdderOpenCL.cpp`)
- **GPU-accelerated** array addition using OpenCL
- **Multi-threaded CPU** fallback
- **Single-threaded CPU** baseline
- Automatic device detection and selection

## Functions

### Core Functions

- `float* addarr(float A[], float B[], int N)`: Single-threaded CPU element-wise addition
- `float* addarr_multithreaded(float A[], float B[], int N)`: Multi-threaded CPU addition
- `float* addarr_opencl(float A[], float B[], int N)`: **OpenCL GPU-accelerated addition**
- `float* generateRandomArray(int N, float min, float max)`: Generates an array of random floats
- `void printArray(const char* label, float* arr, int N)`: Prints array contents (filtered for powers of 2)
- `float* timed(function_ptr, A, B, N, method_name)`: Wrapper function that times array operations

### OpenCL Functions

- `bool initializeOpenCL()`: Sets up OpenCL context, device, and kernel
- `void cleanupOpenCL()`: Releases OpenCL resources
- `std::string loadKernelSource(filename)`: Loads OpenCL kernel from file
- `void checkOpenCLError(error, operation)`: Error handling for OpenCL operations

### Test Functions

- `void testRandomArrays(int N)`: Main testing function comparing all implementations
- `int main()`: Runs performance tests with exponentially increasing array sizes

## Prerequisites

### For AMD GPU Support
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y opencl-headers ocl-icd-libopencl1 ocl-icd-opencl-dev mesa-opencl-icd clinfo

# For AMD GPUs, you may also need:
sudo apt install -y mesa-opencl-icd

# Verify OpenCL installation
clinfo
```

### For AMD GPU Drivers (on real hardware)
```bash
# Install AMD GPU drivers (AMDGPU-PRO or Mesa)
# Follow AMD's official installation guide for your specific GPU
```

## Compilation

### Method 1: Using CMake (Recommended)
```bash
cd GPUPractice
mkdir build && cd build
cmake ..
make

# This builds both versions:
# - arrayAdder (original CPU-only version)
# - arrayAdderOpenCL (GPU-accelerated version)
```

### Method 2: Manual Compilation

#### Original CPU version:
```bash
g++ -O2 -pthread -o arrayAdder arrayAdder.cpp
```

#### OpenCL GPU version:
```bash
g++ -O2 -pthread -o arrayAdderOpenCL arrayAdderOpenCL.cpp -lOpenCL
```

## Usage

### Running the Original Version
```bash
./arrayAdder
```

### Running the OpenCL GPU Version
```bash
# Make sure the kernel file is in the same directory
cp ../addarr_kernel.cl .
./arrayAdderOpenCL
```

The program will automatically:
1. **Initialize OpenCL** and detect available GPU/CPU devices
2. Start with arrays of size 2
3. Increase size by factor of 10 each iteration (2, 20, 200, 2000, ...)
4. Generate two random arrays for each size
5. **Compare performance** across all available implementations:
   - Single-threaded CPU
   - Multi-threaded CPU
   - OpenCL GPU (if available)
6. Display timing information for each method
7. Show array contents only for power-of-2 sizes
8. Verify that all implementations produce identical results

## Sample Output

### OpenCL GPU Version Output
```
--- Testing Random Arrays with OpenCL ---
Using OpenCL device: AMD Radeon RX 6800 XT
OpenCL initialized successfully!

Iteration: 2 - Testing random arrays of size 2
Generating two random arrays of size 2
Array A: 1.79483 4.56614
Array B: 4.13143 4.50899
Single-threaded: Time: 0 microseconds (CPU Single)
Multi-threaded:  Time: 1 microseconds (CPU Multi)
OpenCL GPU:      Time: 45 microseconds (OpenCL GPU)
Sum A+B (single): 5.92626 9.07513
Sum A+B (multi): 5.92626 9.07513
Sum A+B (OpenCL): 5.92626 9.07513
Results match: YES

Iteration: 20000 - Testing random arrays of size 20000
Generating two random arrays of size 20000
Single-threaded: Time: 30 microseconds (CPU Single)
Multi-threaded:  Time: 15 microseconds (CPU Multi)
OpenCL GPU:      Time: 8 microseconds (OpenCL GPU)
```

### Fallback Mode (No GPU Available)
```
--- Testing Random Arrays with OpenCL ---
No GPU found, trying CPU...
No OpenCL devices found
OpenCL initialization failed. Running CPU-only tests.

Iteration: 2 - Testing random arrays of size 2
Single-threaded: Time: 0 microseconds (CPU Single)
Multi-threaded:  Time: 1 microseconds (CPU Multi)
Results match: YES

Iteration: 200 - Testing random arrays of size 200
Generating two random arrays of size 200
Time: 234 microseconds
...
```

## Performance Characteristics

- **Array A Range**: Random floats between 1.0 and 5.0
- **Array B Range**: Random floats between 2.0 and 8.0
- **Timing Precision**: Microsecond resolution using `std::chrono::high_resolution_clock`
- **Memory**: Dynamic allocation using `malloc()` with proper cleanup

## Technical Details

### Memory Management
- Uses `malloc()` for dynamic memory allocation
- Includes proper error checking for allocation failures
- Ensures all allocated memory is freed with `free()`

### Random Number Generation
- Uses `std::rand()` with time-based seeding
- Generates uniformly distributed floats within specified ranges
- Seeds only once per program execution

### Output Filtering
- Array contents are only displayed when array size is a power of 2
- Uses bit manipulation `(N & (N - 1)) == 0` to detect powers of 2
- Reduces output volume for large-scale performance testing

## Files Structure

```
GPUPractice/
├── arrayAdder.cpp          # Original CPU-only implementation
├── arrayAdderOpenCL.cpp    # OpenCL GPU-accelerated implementation
├── addarr_kernel.cl        # OpenCL kernel source code
├── CMakeLists.txt          # CMake build configuration
└── README.md               # This documentation
```

## OpenCL Kernel Details

The `addarr_kernel.cl` file contains two OpenCL kernels:

1. **`addarr_kernel`**: Basic parallel array addition
   - Each work item processes one array element
   - Optimal for large arrays with good GPU utilization

2. **`addarr_kernel_optimized`**: Advanced version with local memory optimization
   - Uses local memory for better cache performance
   - Suitable for very large arrays on high-end GPUs

## Requirements

### Basic Requirements
- C++17 or later
- Standard C++ library
- Compatible with GCC, Clang, and MSVC compilers
- CMake 3.10 or later (for CMake builds)

### OpenCL Requirements
- OpenCL 1.2 or later
- OpenCL headers and libraries
- AMD GPU with OpenCL support (or CPU fallback)
- Mesa OpenCL ICD or AMD proprietary drivers

## Performance Expectations

### GPU vs CPU Performance
- **Small arrays** (< 1000 elements): CPU often faster due to GPU setup overhead
- **Medium arrays** (1000-10000 elements): GPU starts showing advantages
- **Large arrays** (> 10000 elements): GPU significantly outperforms CPU
- **Very large arrays** (> 100000 elements): GPU can be 10-50x faster than single-threaded CPU

### AMD GPU Optimization Tips
1. **Array size**: GPU performance scales better with larger arrays
2. **Memory access**: Coalesced memory access patterns improve performance
3. **Work group size**: Optimal size depends on your specific AMD GPU
4. **Local memory**: Use local memory for frequently accessed data

## Troubleshooting

### OpenCL Issues
```bash
# Check if OpenCL is properly installed
clinfo

# If no devices found, install AMD drivers:
# For Mesa (open source):
sudo apt install mesa-opencl-icd

# For AMD proprietary drivers, follow AMD's installation guide
```

### Build Issues
```bash
# If OpenCL headers not found:
sudo apt install opencl-headers ocl-icd-opencl-dev

# If linking fails:
sudo apt install ocl-icd-libopencl1
```

## Potential Use Cases

- **GPU Performance Benchmarking**: Compare AMD GPU performance against CPU
- **OpenCL Learning**: Educational tool for understanding OpenCL programming
- **Memory Bandwidth Testing**: Measure memory transfer speeds between CPU and GPU
- **Parallel Computing Research**: Baseline for more complex parallel algorithms
- **Hardware Evaluation**: Test different AMD GPU models and configurations

## Notes

- The OpenCL version automatically falls back to CPU if no GPU is available
- GPU performance benefits are most apparent with larger array sizes (> 10000 elements)
- Array contents are only shown for power-of-2 sizes to keep output manageable
- All timing measurements are in microseconds
- The program tests arrays up to 100,000 elements by default (reduced from INT32_MAX for practical testing)
