cmake_minimum_required(VERSION 3.10)
project(ArrayAdder)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(OpenCL REQUIRED)
find_package(Threads REQUIRED)

# Include directories
include_directories(${OpenCL_INCLUDE_DIRS})

# Add executable for original version
add_executable(arrayAdder arrayAdder.cpp)
target_link_libraries(arrayAdder Threads::Threads)

# Add executable for OpenCL version
# add_executable(arrayAdderOpenCL arrayAdderOpenCL.cpp)
# target_link_libraries(arrayAdderOpenCL ${OpenCL_LIBRARIES} Threads::Threads)

# Set compiler flags
target_compile_options(arrayAdder PRIVATE -O2 -Wall)
# target_compile_options(arrayAdderOpenCL PRIVATE -O2 -Wall)

# Add definitions for OpenCL
# target_compile_definitions(arrayAdderOpenCL PRIVATE CL_TARGET_OPENCL_VERSION=120)
